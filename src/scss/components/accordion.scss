@use '../abstracts/variables' as variables;

.accordion {
    width: 1040px;
    max-width: 100%;
    margin: 0 auto;

    h3, p {
        text-align: left;
    }

    h3 {
        margin-bottom: 0;
        color: variables.$light-blue;
        display: flex;
        align-items: center;
        user-select: none;
        transition: color 0.3s ease-in-out;

        &:before {
            font-family: "Font Awesome 6 Pro";
            content: "\f138";
            font-weight: 900;
            margin-right: 25px;
            font-size: 28px;
            -moz-osx-font-smoothing: grayscale;
            -webkit-font-smoothing: antialiased;
            display: inline-block;
            font-style: normal;
            font-variant: normal;
            line-height: 1;
            text-rendering: auto;
            transition: transform 0.15s ease-in-out;
        }

        span {
            position: relative;
            display: inline;
            text-decoration: underline;
            text-decoration-color: transparent;
            text-decoration-thickness: 1px;
            text-underline-offset: 3px;
            transition: text-decoration-color 0.3s ease-in-out;
        }
    }

    .accordion-item {
        margin-bottom: 20px;
        overflow: hidden;

        &:last-child {
            margin-bottom: 0;
        }
    }

    .accordion-header {
        padding: 5.25px 0 9px;
        cursor: pointer;

        @media screen and (max-width: variables.$breakpoint-sm-max) {
            padding: 1.5px 0 3px;
        }

        &:hover {
            @media screen and (min-width: variables.$breakpoint-md-min) {
                h3 {
                    color: variables.$dark-blue;

                    span {
                        text-decoration-color: variables.$dark-blue;

                        // Fallback animation for browsers that don't support text-decoration-color
                        @supports not (text-decoration-color: transparent) {
                            background-size: 100% 1px;

                            &::after {
                                width: 100%;
                            }
                        }
                    }
                }
            }
        }
    }

    .accordion-content {
        padding-left: 53px;
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease-in-out;

        > :last-child {
            margin-bottom: 0;
        }
        
        // Add padding to create space for content
        > * {
            padding-top: 10px;
            padding-bottom: 10px;
        }
    }

    .accordion-item.active {
        .accordion-header h3:before {
            transform: rotate(90deg);
        }

        .accordion-content {
            max-height: 1000px; // Large enough value to accommodate content
        }
    }
}
